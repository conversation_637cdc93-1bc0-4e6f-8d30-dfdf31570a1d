import { DashboardLayout } from "../components/layout/DashboardLayout";
import { FileUpload } from "../components/files/FileUpload";
import { useNavigate, useSearchParams } from "react-router-dom";
import { usePageTitle } from "../hooks/usePageTitle";
import { SEOHead } from "../components/seo/SEOHead";
import { getPageSEOData } from "../lib/seo";
import toast from "react-hot-toast";

export function Upload() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Set page title
  usePageTitle("Upload Documents");

  // Generate SEO metadata
  const seoMetadata = getPageSEOData("/upload");

  // Get folder ID from URL params if provided
  const folderId = searchParams.get("folderId");

  const handleUploadComplete = (document: any) => {
    toast.success(`Document "${document.title}" uploaded successfully!`);
    // Redirect to documents page after successful upload
    navigate("/documents");
  };

  return (
    <>
      {/* SEO Meta Tags */}
      <SEOHead metadata={seoMetadata} />

      <DashboardLayout>
        <div className="py-6">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                Upload Documents
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                Upload and organize your documents
              </p>
            </div>

            {/* File Upload Component */}
            <FileUpload
              onUploadComplete={handleUploadComplete}
              currentFolderId={folderId}
              showFolderSelector={true}
              maxFiles={10}
            />
          </div>
        </div>
      </DashboardLayout>
    </>
  );
}
