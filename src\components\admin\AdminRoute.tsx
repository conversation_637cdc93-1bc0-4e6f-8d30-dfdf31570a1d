import { Navigate, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { isAdminRole } from "../../lib/admin/adminUtils";
import { BrimBagLogo } from "../ui/BrimBagLogo";

interface AdminRouteProps {
  children: React.ReactNode;
}

export function AdminRoute({ children }: AdminRouteProps) {
  const { user, loading } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900 transition-theme duration-theme">
        <div className="text-center">
          <div className="mb-4">
            <BrimBagLogo size="xl" className="mx-auto animate-pulse" />
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
            Verifying admin access...
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    // Redirect to admin login page with return url
    return <Navigate to="/admin/login" state={{ from: location }} replace />;
  }

  if (!isAdminRole(user.profile?.role)) {
    // Show access denied page for non-admin users
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900 transition-theme duration-theme">
        <div className="max-w-md w-full bg-white dark:bg-dark-800 rounded-lg shadow-lg p-8 text-center transition-theme duration-theme">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center transition-theme duration-theme">
            <svg
              className="w-8 h-8 text-red-600 dark:text-red-400 transition-theme duration-theme"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-theme duration-theme">
            Access Denied
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6 transition-theme duration-theme">
            You don't have permission to access the admin panel. Please contact
            your system administrator if you believe this is an error.
          </p>
          <div className="space-y-3">
            <button
              onClick={() => window.history.back()}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
            >
              Go Back
            </button>
            <button
              onClick={() => navigate("/dashboard")}
              className="w-full bg-gray-100 hover:bg-gray-200 dark:bg-dark-700 dark:hover:bg-dark-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-all duration-200"
            >
              Return to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Higher-order component for admin route protection
export function withAdminRoute<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return function AdminProtectedComponent(props: P) {
    return (
      <AdminRoute>
        <Component {...props} />
      </AdminRoute>
    );
  };
}

// Hook to check admin access
export function useAdminAccess() {
  const { user, loading } = useAuth();

  return {
    isAdmin: isAdminRole(user?.profile?.role),
    isLoading: loading,
    user,
  };
}
