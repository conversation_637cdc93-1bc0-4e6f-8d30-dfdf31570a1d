import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { roomService } from "../../lib/roomService";
// Simple date formatting without external dependency
import toast from "react-hot-toast";

interface RoomDetails {
  id: string;
  name: string;
  description: string | null;
  room_code: string;
  is_private: boolean;
  member_count: number;
  created_at: string;
  created_by: string;
  creator_name: string | null;
  is_member: boolean;
}

interface RoomDetailsModalProps {
  roomId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function RoomDetailsModal({
  roomId,
  isOpen,
  onClose,
}: RoomDetailsModalProps) {
  const { user } = useAuth();
  const [room, setRoom] = useState<RoomDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isJoining, setIsJoining] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && roomId && user) {
      fetchRoomDetails();
    }
  }, [isOpen, roomId, user]);

  const fetchRoomDetails = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get room details
      const roomData = await roomService.getRoomById(roomId);

      // Check if user is already a member
      const members = await roomService.getRoomMembers(roomId);
      const isMember = members.some((member) => member.user_id === user.id);

      // Get creator name
      const creator = members.find(
        (member) => member.user_id === roomData.created_by
      );

      setRoom({
        ...roomData,
        is_member: isMember,
        member_count: members.length,
        creator_name: creator?.profiles?.full_name || null,
      });
    } catch (error: any) {
      console.error("Error fetching room details:", error);
      setError("Failed to load room details");
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinRoom = async () => {
    if (!user || !room) return;

    setIsJoining(true);
    try {
      await roomService.joinRoom(room.room_code, user.id);
      toast.success(`Successfully joined "${room.name}"`);

      // Update room state to reflect membership
      setRoom((prev) =>
        prev
          ? { ...prev, is_member: true, member_count: prev.member_count + 1 }
          : null
      );
    } catch (error: any) {
      console.error("Error joining room:", error);
      toast.error(error.message || "Failed to join room");
    } finally {
      setIsJoining(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto transition-theme duration-theme">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Room Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-red-600 dark:text-red-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <p className="text-red-600 dark:text-red-400">{error}</p>
            </div>
          ) : room ? (
            <div className="space-y-6">
              {/* Room Header */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                  <svg
                    className="w-8 h-8 text-purple-600 dark:text-purple-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {room.name}
                </h3>
                {room.description && (
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {room.description}
                  </p>
                )}
              </div>

              {/* Room Details */}
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Room Code
                    </label>
                    <div className="flex items-center space-x-2">
                      <code className="bg-gray-100 dark:bg-dark-700 px-2 py-1 rounded text-sm font-mono">
                        {room.room_code}
                      </code>
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(room.room_code);
                          toast.success("Room code copied!");
                        }}
                        className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Members
                    </label>
                    <p className="text-gray-900 dark:text-gray-100">
                      {room.member_count}
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Created By
                  </label>
                  <p className="text-gray-900 dark:text-gray-100">
                    {room.creator_name || "Unknown"}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Created
                  </label>
                  <p className="text-gray-900 dark:text-gray-100">
                    {new Date(room.created_at).toLocaleDateString()}
                  </p>
                </div>

                {/* Membership Status */}
                <div className="flex items-center space-x-2">
                  {room.is_member ? (
                    <div className="flex items-center text-green-600 dark:text-green-400">
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span className="text-sm font-medium">
                        You are a member
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center text-gray-500 dark:text-gray-400">
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                        />
                      </svg>
                      <span className="text-sm font-medium">Not a member</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-3 pt-4 border-t border-gray-200 dark:border-dark-700">
                {room.is_member ? (
                  <button
                    onClick={() => {
                      // Navigate to room page
                      window.location.href = `/rooms/${room.id}`;
                    }}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    View Room
                  </button>
                ) : (
                  <button
                    onClick={handleJoinRoom}
                    disabled={isJoining}
                    className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                  >
                    {isJoining ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Joining...
                      </>
                    ) : (
                      "Join Room"
                    )}
                  </button>
                )}
                <button
                  onClick={onClose}
                  className="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-dark-700 dark:hover:bg-dark-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                >
                  Close
                </button>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}
