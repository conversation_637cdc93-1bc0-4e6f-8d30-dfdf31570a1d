import { Helmet } from "react-helmet-async";
import type { SEOMetaData } from "../../lib/seo";
import {
  generateOpenGraphData,
  generateTwitterCardData,
  generateStructuredData,
} from "../../lib/seo";

interface SEOHeadProps {
  metadata: SEOMetaData;
  includeStructuredData?: boolean;
}

/**
 * SEOHead component for managing all SEO-related meta tags
 * Uses React Helmet Async for dynamic meta tag management
 */
export function SEOHead({
  metadata,
  includeStructuredData = false,
}: SEOHeadProps) {
  const openGraphData = generateOpenGraphData(metadata);
  const twitterCardData = generateTwitterCardData(metadata);
  const structuredData = includeStructuredData
    ? generateStructuredData()
    : null;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{metadata.title}</title>
      <meta name="description" content={metadata.description} />
      {metadata.keywords && (
        <meta name="keywords" content={metadata.keywords} />
      )}

      {/* Canonical URL */}
      {metadata.canonical && <link rel="canonical" href={metadata.canonical} />}

      {/* Robots Meta Tag */}
      {metadata.noIndex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow" />
      )}

      {/* Open Graph Tags */}
      <meta property="og:title" content={openGraphData.title} />
      <meta property="og:description" content={openGraphData.description} />
      <meta property="og:image" content={openGraphData.image} />
      <meta property="og:url" content={openGraphData.url} />
      <meta property="og:type" content={openGraphData.type} />
      <meta property="og:site_name" content={openGraphData.siteName} />

      {/* Twitter Card Tags */}
      <meta name="twitter:card" content={twitterCardData.card} />
      <meta name="twitter:title" content={twitterCardData.title} />
      <meta name="twitter:description" content={twitterCardData.description} />
      <meta name="twitter:image" content={twitterCardData.image} />
      {twitterCardData.site && (
        <meta name="twitter:site" content={twitterCardData.site} />
      )}

      {/* PWA Meta Tags */}
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="BrimBag" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="application-name" content="BrimBag" />

      {/* Theme Color */}
      <meta name="theme-color" content="#1E90FF" />
      <meta name="msapplication-TileColor" content="#1E90FF" />

      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
}

/**
 * Hook for easy SEO management in components
 */
export function useSEO(metadata: SEOMetaData, includeStructuredData?: boolean) {
  return (
    <SEOHead
      metadata={metadata}
      includeStructuredData={includeStructuredData}
    />
  );
}
