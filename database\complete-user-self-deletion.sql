-- Complete User Self-Deletion System
-- This script enables users to completely delete their own accounts including from auth.users
-- Run this SQL in your Supabase SQL Editor

-- Create a function specifically for user self-deletion
CREATE OR REPLACE FUNCTION delete_own_account(p_user_id UUID)
R<PERSON><PERSON>NS JSON AS $$
DECLARE
  deleted_counts JSON;
  user_email TEXT;
  user_name TEXT;
  documents INTEGER := 0;
  folders INTEGER := 0;
  room_memberships INTEGER := 0;
  rooms_created INTEGER := 0;
  document_shares INTEGER := 0;
  activity_logs INTEGER := 0;
  notifications INTEGER := 0;
  feedback INTEGER := 0;
  room_invitations INTEGER := 0;
  room_invitation_links INTEGER := 0;
  shared_documents_nullified INTEGER := 0;
BEGIN
  -- For self-deletion, verify that the user is deleting their own account
  -- For admin deletion, allow admins to delete other users
  IF auth.uid() != p_user_id THEN
    -- Check if current user is admin for admin deletion
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
      RAISE EXCEPTION 'Access denied: You can only delete your own account or admin role required';
    END IF;
  ELSE
    -- Prevent admin users from deleting their own accounts
    IF EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND role = 'admin') THEN
      RAISE EXCEPTION 'Admin users cannot delete their own account. Please contact another admin.';
    END IF;
  END IF;
  
  -- Get user info for logging
  SELECT email, full_name INTO user_email, user_name
  FROM profiles WHERE id = p_user_id;
  
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;
  
  -- Count records before deletion for logging
  SELECT 
    COALESCE((SELECT COUNT(*) FROM documents WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM folders WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM room_members WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM rooms WHERE created_by = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM activity_logs WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM notifications WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM user_feedback WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM room_invitation_links WHERE created_by = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM documents WHERE shared_from_user_id = p_user_id), 0)
  INTO documents, folders, room_memberships, rooms_created, document_shares, 
       activity_logs, notifications, feedback, room_invitations, room_invitation_links, shared_documents_nullified;
  
  -- Build the deletion summary
  SELECT json_build_object(
    'user_email', user_email,
    'user_name', user_name,
    'deletion_type', 'self_deletion',
    'documents_deleted', documents,
    'folders_deleted', folders,
    'room_memberships_removed', room_memberships,
    'rooms_deleted', rooms_created,
    'document_shares_removed', document_shares,
    'activity_logs_removed', activity_logs,
    'notifications_removed', notifications,
    'feedback_removed', feedback,
    'room_invitations_removed', room_invitations,
    'room_invitation_links_removed', room_invitation_links,
    'shared_documents_nullified', shared_documents_nullified,
    'deleted_at', NOW()
  ) INTO deleted_counts;
  
  -- Delete user data in correct order (respecting foreign key constraints)
  
  -- Delete activity logs
  DELETE FROM activity_logs WHERE user_id = p_user_id;
  
  -- Delete document shares
  DELETE FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  
  -- Delete room documents shared by this user
  DELETE FROM room_documents WHERE shared_by = p_user_id;
  
  -- Delete room memberships
  DELETE FROM room_members WHERE user_id = p_user_id;
  
  -- Delete rooms created by user (this will cascade to room_documents and room_members)
  DELETE FROM rooms WHERE created_by = p_user_id;
  
  -- Nullify shared_from_user_id for documents shared by this user
  -- This preserves shared documents for recipients but removes the reference to deleted user
  UPDATE documents SET shared_from_user_id = NULL WHERE shared_from_user_id = p_user_id;

  -- Delete documents owned by user
  DELETE FROM documents WHERE user_id = p_user_id;
  
  -- Delete folders
  DELETE FROM folders WHERE user_id = p_user_id;
  
  -- Delete notifications
  DELETE FROM notifications WHERE user_id = p_user_id;

  -- Delete user feedback
  DELETE FROM user_feedback WHERE user_id = p_user_id;

  -- Delete room invitations
  DELETE FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id;

  -- Delete room invitation links
  DELETE FROM room_invitation_links WHERE created_by = p_user_id;
  
  -- Finally delete the profile
  DELETE FROM profiles WHERE id = p_user_id;

  -- Note: The auth.users record will be deleted by the client-side admin API call
  -- This ensures complete account deletion including authentication record
  
  RETURN deleted_counts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (for self-deletion)
GRANT EXECUTE ON FUNCTION delete_own_account(UUID) TO authenticated;

-- Update the existing admin_delete_user function to handle both admin and self-deletion
-- This ensures consistency between admin deletion and self-deletion
CREATE OR REPLACE FUNCTION admin_delete_user(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  deleted_counts JSON;
  user_email TEXT;
  user_name TEXT;
  is_self_deletion BOOLEAN := false;
  is_admin_deletion BOOLEAN := false;
  documents INTEGER := 0;
  folders INTEGER := 0;
  room_memberships INTEGER := 0;
  rooms_created INTEGER := 0;
  document_shares INTEGER := 0;
  activity_logs INTEGER := 0;
  notifications INTEGER := 0;
  feedback INTEGER := 0;
  room_invitations INTEGER := 0;
  room_invitation_links INTEGER := 0;
  shared_documents_nullified INTEGER := 0;
BEGIN
  -- Check if this is self-deletion
  IF auth.uid() = p_user_id THEN
    is_self_deletion := true;
    -- Prevent admin self-deletion
    IF EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
      RAISE EXCEPTION 'Admin users cannot delete their own account. Please contact another admin.';
    END IF;
  ELSE
    -- Check if current user is admin for admin deletion
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
      RAISE EXCEPTION 'Access denied: Admin role required to delete other users';
    END IF;
    is_admin_deletion := true;
  END IF;

  -- Get user info for logging
  SELECT email, full_name INTO user_email, user_name
  FROM profiles WHERE id = p_user_id;

  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Count records before deletion for logging
  SELECT
    COALESCE((SELECT COUNT(*) FROM documents WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM folders WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM room_members WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM rooms WHERE created_by = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM activity_logs WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM notifications WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM user_feedback WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM room_invitation_links WHERE created_by = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM documents WHERE shared_from_user_id = p_user_id), 0)
  INTO documents, folders, room_memberships, rooms_created, document_shares,
       activity_logs, notifications, feedback, room_invitations, room_invitation_links, shared_documents_nullified;

  -- Build the deletion summary
  SELECT json_build_object(
    'user_email', user_email,
    'user_name', user_name,
    'deletion_type', CASE
      WHEN is_self_deletion THEN 'self_deletion'
      WHEN is_admin_deletion THEN 'admin_deletion'
      ELSE 'unknown'
    END,
    'deleted_by', auth.uid(),
    'documents_deleted', documents,
    'folders_deleted', folders,
    'room_memberships_removed', room_memberships,
    'rooms_deleted', rooms_created,
    'document_shares_removed', document_shares,
    'activity_logs_removed', activity_logs,
    'notifications_removed', notifications,
    'feedback_removed', feedback,
    'room_invitations_removed', room_invitations,
    'room_invitation_links_removed', room_invitation_links,
    'shared_documents_nullified', shared_documents_nullified,
    'deleted_at', NOW()
  ) INTO deleted_counts;

  -- Delete user data in correct order (respecting foreign key constraints)

  -- Delete activity logs
  DELETE FROM activity_logs WHERE user_id = p_user_id;

  -- Delete document shares
  DELETE FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;

  -- Delete room documents shared by this user
  DELETE FROM room_documents WHERE shared_by = p_user_id;

  -- Delete room memberships
  DELETE FROM room_members WHERE user_id = p_user_id;

  -- Delete rooms created by user (this will cascade to room_documents and room_members)
  DELETE FROM rooms WHERE created_by = p_user_id;

  -- Nullify shared_from_user_id for documents shared by this user
  -- This preserves shared documents for recipients but removes the reference to deleted user
  UPDATE documents SET shared_from_user_id = NULL WHERE shared_from_user_id = p_user_id;

  -- Delete documents owned by user
  DELETE FROM documents WHERE user_id = p_user_id;

  -- Delete folders
  DELETE FROM folders WHERE user_id = p_user_id;

  -- Delete notifications
  DELETE FROM notifications WHERE user_id = p_user_id;

  -- Delete user feedback
  DELETE FROM user_feedback WHERE user_id = p_user_id;

  -- Delete room invitations
  DELETE FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id;

  -- Delete room invitation links
  DELETE FROM room_invitation_links WHERE created_by = p_user_id;

  -- Finally delete the profile
  DELETE FROM profiles WHERE id = p_user_id;

  -- Log admin action (only for admin deletions, not self-deletions)
  IF is_admin_deletion THEN
    PERFORM log_admin_action(
      'user_delete',
      'user',
      p_user_id,
      deleted_counts
    );
  END IF;

  RETURN deleted_counts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure proper permissions
GRANT EXECUTE ON FUNCTION admin_delete_user(UUID) TO authenticated;

-- Create a view for deletion audit (optional - for tracking deletions)
CREATE OR REPLACE VIEW user_deletion_audit AS
SELECT
  aa.id,
  aa.admin_id as deleted_by,
  aa.target_id as deleted_user_id,
  aa.details->>'user_email' as deleted_user_email,
  aa.details->>'user_name' as deleted_user_name,
  aa.details->>'deletion_type' as deletion_type,
  aa.details->>'documents_deleted' as documents_deleted,
  aa.details->>'folders_deleted' as folders_deleted,
  aa.created_at as deletion_timestamp
FROM admin_actions aa
WHERE aa.action_type = 'user_delete'
ORDER BY aa.created_at DESC;

-- Grant view access to admins
GRANT SELECT ON user_deletion_audit TO authenticated;

-- Add RLS policy for the audit view (if not already exists)
DROP POLICY IF EXISTS "Admins can view deletion audit" ON admin_actions;
CREATE POLICY "Admins can view deletion audit" ON admin_actions
FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);
