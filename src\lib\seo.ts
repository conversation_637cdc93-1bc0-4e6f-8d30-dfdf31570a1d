// SEO constants and utility functions for BrimBag platform

export const SEO_CONSTANTS = {
  SITE_NAME: "BrimBag",
  SITE_URL: "https://brimbag.com", // Update with actual domain
  CONTACT_EMAIL: "<EMAIL>",
  TWITTER_HANDLE: "@brimbag", // Update with actual Twitter handle
  DEFAULT_IMAGE: "/android-chrome-512x512.png", // Using existing PWA icon
  THEME_COLOR: "#1E90FF",

  // Default meta descriptions
  DEFAULT_DESCRIPTION:
    "BrimBag - Your digital document storage and sharing platform for students and educators. Replace your physical bag with secure cloud storage.",

  // Page-specific descriptions
  DESCRIPTIONS: {
    HOME: "Replace your physical bag with digital storage. BrimBag is the secure document sharing platform designed for students and educators.",
    LOGIN:
      "Sign in to BrimBag to access your digital documents, share files with classmates, and organize your academic materials securely.",
    REGISTER:
      "Join <PERSON><PERSON><PERSON><PERSON> today! Create your free account to start organizing, storing, and sharing your academic documents digitally.",
    FORGOT_PASSWORD:
      "Reset your BrimBag password to regain access to your digital document storage and sharing platform.",
    RESET_PASSWORD:
      "Create a new password for your BrimBag account to continue accessing your documents and sharing features.",
    DASHBOARD:
      "Your BrimBag dashboard - Overview of your documents, storage usage, recent activity, and quick access to all platform features.",
    DOCUMENTS:
      "Manage your digital documents in BrimBag. Upload, organize, preview, and share your academic files with ease.",
    UPLOAD:
      "Upload documents to BrimBag. Drag and drop your files to securely store and organize your academic materials.",
    ROOMS:
      "Join and create document sharing rooms in BrimBag. Collaborate with classmates and share course materials efficiently.",
    SHARED_DOCUMENTS:
      "View documents shared with you in BrimBag. Access files from classmates, instructors, and study groups.",
    PROFILE:
      "Manage your BrimBag profile settings, account preferences, and personal information for your document sharing platform.",
    NOTIFICATIONS:
      "Stay updated with BrimBag notifications. View room invitations, document shares, and platform updates.",
    FEEDBACK:
      "Share your feedback about BrimBag. Help us improve the platform for students and educators worldwide.",
  },

  // Keywords for different page types
  KEYWORDS: {
    GENERAL:
      "document storage, file sharing, student platform, academic documents, digital bag, cloud storage, education technology",
    HOME: "digital document storage, student file sharing, replace physical bag, academic organization, secure cloud storage",
    AUTH: "student login, academic account, document platform access, secure authentication",
    DASHBOARD:
      "document management, file organization, storage dashboard, academic productivity",
    SHARING:
      "document sharing, file collaboration, student groups, academic teamwork",
  },
};

export interface SEOMetaData {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  noIndex?: boolean;
  canonical?: string;
}

export interface OpenGraphData {
  title: string;
  description: string;
  image: string;
  url: string;
  type: string;
  siteName: string;
}

export interface TwitterCardData {
  card: string;
  title: string;
  description: string;
  image: string;
  site?: string;
}

export interface StructuredData {
  "@context": string;
  "@type": string;
  name: string;
  description: string;
  url: string;
  applicationCategory: string;
  operatingSystem: string;
  offers?: {
    "@type": string;
    price: string;
    priceCurrency: string;
  };
  author?: {
    "@type": string;
    name: string;
  };
}

/**
 * Generate complete SEO metadata for a page
 */
export function generateSEOMetadata(options: {
  title: string;
  description?: string;
  keywords?: string;
  image?: string;
  path?: string;
  noIndex?: boolean;
  type?: string;
}): SEOMetaData {
  const {
    title,
    description = SEO_CONSTANTS.DEFAULT_DESCRIPTION,
    keywords = SEO_CONSTANTS.KEYWORDS.GENERAL,
    image = SEO_CONSTANTS.DEFAULT_IMAGE,
    path = "",
    noIndex = false,
    type = "website",
  } = options;

  const fullTitle = title.includes(SEO_CONSTANTS.SITE_NAME)
    ? title
    : `${title} - ${SEO_CONSTANTS.SITE_NAME}`;

  const url = `${SEO_CONSTANTS.SITE_URL}${path}`;
  const canonical = url;

  return {
    title: fullTitle,
    description,
    keywords,
    image,
    url,
    type,
    noIndex,
    canonical,
  };
}

/**
 * Generate Open Graph metadata
 */
export function generateOpenGraphData(metadata: SEOMetaData): OpenGraphData {
  return {
    title: metadata.title,
    description: metadata.description,
    image: metadata.image?.startsWith("http")
      ? metadata.image
      : `${SEO_CONSTANTS.SITE_URL}${metadata.image}`,
    url: metadata.url || SEO_CONSTANTS.SITE_URL,
    type: metadata.type || "website",
    siteName: SEO_CONSTANTS.SITE_NAME,
  };
}

/**
 * Generate Twitter Card metadata
 */
export function generateTwitterCardData(
  metadata: SEOMetaData
): TwitterCardData {
  return {
    card: "summary_large_image",
    title: metadata.title,
    description: metadata.description,
    image: metadata.image?.startsWith("http")
      ? metadata.image
      : `${SEO_CONSTANTS.SITE_URL}${metadata.image}`,
    site: SEO_CONSTANTS.TWITTER_HANDLE,
  };
}

/**
 * Generate JSON-LD structured data for the application
 */
export function generateStructuredData(): StructuredData {
  return {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: SEO_CONSTANTS.SITE_NAME,
    description: SEO_CONSTANTS.DEFAULT_DESCRIPTION,
    url: SEO_CONSTANTS.SITE_URL,
    applicationCategory: "EducationalApplication",
    operatingSystem: "Web Browser",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
    },
    author: {
      "@type": "Organization",
      name: SEO_CONSTANTS.SITE_NAME,
    },
  };
}

/**
 * Generate organization structured data
 */
export function generateOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: SEO_CONSTANTS.SITE_NAME,
    url: SEO_CONSTANTS.SITE_URL,
    logo: `${SEO_CONSTANTS.SITE_URL}${SEO_CONSTANTS.DEFAULT_IMAGE}`,
    description: SEO_CONSTANTS.DEFAULT_DESCRIPTION,
    contactPoint: {
      "@type": "ContactPoint",
      email: SEO_CONSTANTS.CONTACT_EMAIL,
      contactType: "customer service",
    },
    sameAs: [
      // Add social media URLs when available
    ],
  };
}

/**
 * Generate breadcrumb structured data for navigation
 */
export function generateBreadcrumbStructuredData(
  breadcrumbs: Array<{ name: string; url: string }>
) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };
}

/**
 * Get page-specific SEO data based on route
 */
export function getPageSEOData(
  route: string,
  dynamicData?: { [key: string]: string }
): SEOMetaData {
  const routeMap: {
    [key: string]: { title: string; description: string; keywords: string };
  } = {
    "/": {
      title: "Home",
      description: SEO_CONSTANTS.DESCRIPTIONS.HOME,
      keywords: SEO_CONSTANTS.KEYWORDS.HOME,
    },
    "/login": {
      title: "Login",
      description: SEO_CONSTANTS.DESCRIPTIONS.LOGIN,
      keywords: SEO_CONSTANTS.KEYWORDS.AUTH,
    },
    "/register": {
      title: "Sign Up",
      description: SEO_CONSTANTS.DESCRIPTIONS.REGISTER,
      keywords: SEO_CONSTANTS.KEYWORDS.AUTH,
    },
    "/forgot-password": {
      title: "Reset Password",
      description: SEO_CONSTANTS.DESCRIPTIONS.FORGOT_PASSWORD,
      keywords: SEO_CONSTANTS.KEYWORDS.AUTH,
    },
    "/forgot-password/reset": {
      title: "Reset Password",
      description: SEO_CONSTANTS.DESCRIPTIONS.RESET_PASSWORD,
      keywords: SEO_CONSTANTS.KEYWORDS.AUTH,
    },
    "/dashboard": {
      title: "Dashboard",
      description: SEO_CONSTANTS.DESCRIPTIONS.DASHBOARD,
      keywords: SEO_CONSTANTS.KEYWORDS.DASHBOARD,
    },
    "/documents": {
      title: "My Documents",
      description: SEO_CONSTANTS.DESCRIPTIONS.DOCUMENTS,
      keywords: SEO_CONSTANTS.KEYWORDS.DASHBOARD,
    },
    "/upload": {
      title: "Upload Documents",
      description: SEO_CONSTANTS.DESCRIPTIONS.UPLOAD,
      keywords: SEO_CONSTANTS.KEYWORDS.DASHBOARD,
    },
    "/rooms": {
      title: "Rooms",
      description: SEO_CONSTANTS.DESCRIPTIONS.ROOMS,
      keywords: SEO_CONSTANTS.KEYWORDS.SHARING,
    },
    "/shared": {
      title: "Shared Documents",
      description: SEO_CONSTANTS.DESCRIPTIONS.SHARED_DOCUMENTS,
      keywords: SEO_CONSTANTS.KEYWORDS.SHARING,
    },
    "/profile": {
      title: "Profile",
      description: SEO_CONSTANTS.DESCRIPTIONS.PROFILE,
      keywords: SEO_CONSTANTS.KEYWORDS.DASHBOARD,
    },
    "/notifications": {
      title: "Notifications",
      description: SEO_CONSTANTS.DESCRIPTIONS.NOTIFICATIONS,
      keywords: SEO_CONSTANTS.KEYWORDS.GENERAL,
    },
    "/feedback": {
      title: "Feedback",
      description: SEO_CONSTANTS.DESCRIPTIONS.FEEDBACK,
      keywords: SEO_CONSTANTS.KEYWORDS.GENERAL,
    },
  };

  const pageData = routeMap[route] || {
    title: "Page",
    description: SEO_CONSTANTS.DEFAULT_DESCRIPTION,
    keywords: SEO_CONSTANTS.KEYWORDS.GENERAL,
  };

  // Handle dynamic titles (e.g., room names)
  if (dynamicData?.roomName && route.startsWith("/rooms/")) {
    pageData.title = `Room: ${dynamicData.roomName}`;
    pageData.description = `Access documents and collaborate in the ${dynamicData.roomName} room on BrimBag.`;
  }

  return generateSEOMetadata({
    title: pageData.title,
    description: pageData.description,
    keywords: pageData.keywords,
    path: route,
    noIndex:
      route.startsWith("/dashboard") ||
      route.startsWith("/documents") ||
      route.startsWith("/rooms/") ||
      route.startsWith("/shared") ||
      route.startsWith("/profile") ||
      route.startsWith("/notifications") ||
      route.startsWith("/upload") ||
      route.startsWith("/feedback"),
  });
}
