-- Apply Admin Panel Fixes
-- This script applies the necessary database function updates for admin user deletion

-- First, ensure the admin_delete_user function exists and works properly
CREATE OR REPLACE FUNCTION admin_delete_user(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  deleted_counts JSON;
  user_email TEXT;
  user_name TEXT;
  is_self_deletion BOOLEAN := false;
  is_admin_deletion BOOLEAN := false;
  documents INTEGER := 0;
  folders INTEGER := 0;
  room_memberships INTEGER := 0;
  rooms_created INTEGER := 0;
  document_shares INTEGER := 0;
  activity_logs INTEGER := 0;
  notifications INTEGER := 0;
  feedback INTEGER := 0;
  room_invitations INTEGER := 0;
  room_invitation_links INTEGER := 0;
  shared_documents_nullified INTEGER := 0;
BEGIN
  -- Check if this is self-deletion
  IF auth.uid() = p_user_id THEN
    is_self_deletion := true;
    -- Prevent admin self-deletion
    IF EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
      RAISE EXCEPTION 'Admin users cannot delete their own account. Please contact another admin.';
    END IF;
  ELSE
    -- Check if current user is admin for admin deletion
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin') THEN
      RAISE EXCEPTION 'Access denied: Admin role required to delete other users';
    END IF;
    is_admin_deletion := true;
  END IF;
  
  -- Get user info for logging
  SELECT email, full_name INTO user_email, user_name
  FROM profiles WHERE id = p_user_id;
  
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;
  
  -- Count records before deletion for logging
  SELECT 
    COALESCE((SELECT COUNT(*) FROM documents WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM folders WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM room_members WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM rooms WHERE created_by = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM activity_logs WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM notifications WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM user_feedback WHERE user_id = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM room_invitation_links WHERE created_by = p_user_id), 0),
    COALESCE((SELECT COUNT(*) FROM documents WHERE shared_from_user_id = p_user_id), 0)
  INTO documents, folders, room_memberships, rooms_created, document_shares, 
       activity_logs, notifications, feedback, room_invitations, room_invitation_links, shared_documents_nullified;
  
  -- Build the deletion summary
  SELECT json_build_object(
    'user_email', user_email,
    'user_name', user_name,
    'deletion_type', CASE
      WHEN is_self_deletion THEN 'self_deletion'
      WHEN is_admin_deletion THEN 'admin_deletion'
      ELSE 'unknown'
    END,
    'deleted_by', auth.uid(),
    'documents_deleted', documents,
    'folders_deleted', folders,
    'room_memberships_removed', room_memberships,
    'rooms_deleted', rooms_created,
    'document_shares_removed', document_shares,
    'activity_logs_removed', activity_logs,
    'notifications_removed', notifications,
    'feedback_removed', feedback,
    'room_invitations_removed', room_invitations,
    'room_invitation_links_removed', room_invitation_links,
    'shared_documents_nullified', shared_documents_nullified,
    'deleted_at', NOW()
  ) INTO deleted_counts;
  
  -- Delete user data in correct order (respecting foreign key constraints)
  
  -- Delete activity logs
  DELETE FROM activity_logs WHERE user_id = p_user_id;
  
  -- Delete document shares
  DELETE FROM document_shares WHERE shared_by = p_user_id OR shared_with = p_user_id;
  
  -- Delete room documents shared by this user (if table exists)
  DELETE FROM room_documents WHERE shared_by = p_user_id;
  
  -- Delete room memberships
  DELETE FROM room_members WHERE user_id = p_user_id;
  
  -- Delete rooms created by user (this will cascade to room_documents and room_members)
  DELETE FROM rooms WHERE created_by = p_user_id;
  
  -- Nullify shared_from_user_id for documents shared by this user
  -- This preserves shared documents for recipients but removes the reference to deleted user
  UPDATE documents SET shared_from_user_id = NULL WHERE shared_from_user_id = p_user_id;

  -- Delete documents owned by user
  DELETE FROM documents WHERE user_id = p_user_id;
  
  -- Delete folders
  DELETE FROM folders WHERE user_id = p_user_id;
  
  -- Delete notifications (if table exists)
  DELETE FROM notifications WHERE user_id = p_user_id;

  -- Delete user feedback (if table exists)
  DELETE FROM user_feedback WHERE user_id = p_user_id;

  -- Delete room invitations (if table exists)
  DELETE FROM room_invitations WHERE invited_by = p_user_id OR invited_user = p_user_id;

  -- Delete room invitation links (if table exists)
  DELETE FROM room_invitation_links WHERE created_by = p_user_id;
  
  -- Finally delete the profile
  DELETE FROM profiles WHERE id = p_user_id;
  
  -- Log admin action (only for admin deletions, not self-deletions)
  IF is_admin_deletion THEN
    -- Only log if log_admin_action function exists
    BEGIN
      PERFORM log_admin_action(
        'user_delete',
        'user',
        p_user_id,
        deleted_counts
      );
    EXCEPTION
      WHEN undefined_function THEN
        -- Function doesn't exist, skip logging
        NULL;
    END;
  END IF;
  
  RETURN deleted_counts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION admin_delete_user(UUID) TO authenticated;
