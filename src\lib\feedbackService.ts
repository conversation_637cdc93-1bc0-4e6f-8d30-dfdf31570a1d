import { supabase } from "./supabase";
import type { Database } from "./supabase";

export type UserFeedback = Database["public"]["Tables"]["user_feedback"]["Row"];
export type UserFeedbackInsert =
  Database["public"]["Tables"]["user_feedback"]["Insert"];
export type UserFeedbackUpdate =
  Database["public"]["Tables"]["user_feedback"]["Update"];

export type FeedbackCategory =
  | "bug_report"
  | "feature_request"
  | "general"
  | "ui_ux";
export type FeedbackStatus = "pending" | "reviewed" | "resolved";

// Simple cache for feedback stats (3 minute cache)
let feedbackStatsCache: { data: FeedbackStats; timestamp: number } | null =
  null;
const FEEDBACK_CACHE_DURATION = 3 * 60 * 1000; // 3 minutes

export interface FeedbackWithProfile extends UserFeedback {
  profiles: {
    full_name: string | null;
    email: string;
    role: string;
  };
}

export interface FeedbackFilters {
  status?: FeedbackStatus;
  category?: FeedbackCategory;
  rating?: number;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface FeedbackStats {
  totalFeedback: number;
  pendingFeedback: number;
  reviewedFeedback: number;
  resolvedFeedback: number;
  averageRating: number;
  categoryBreakdown: {
    bug_report: number;
    feature_request: number;
    general: number;
    ui_ux: number;
  };
  ratingBreakdown: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  recentFeedback: FeedbackWithProfile[];
}

export interface PaginatedFeedbackResponse {
  data: FeedbackWithProfile[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const feedbackService = {
  // Submit new feedback (user-side)
  async submitFeedback(data: {
    userId: string;
    feedbackText: string;
    rating: number;
    category: FeedbackCategory;
  }): Promise<UserFeedback> {
    try {
      const { data: feedback, error } = await supabase
        .from("user_feedback")
        .insert({
          user_id: data.userId,
          feedback_text: data.feedbackText,
          rating: data.rating,
          category: data.category,
          status: "pending",
        })
        .select()
        .single();

      if (error) throw error;
      return feedback;
    } catch (error: any) {
      console.error("Submit feedback error:", error);
      throw new Error("Unable to submit feedback. Please try again later.");
    }
  },

  // Get user's own feedback
  async getUserFeedback(userId: string): Promise<UserFeedback[]> {
    try {
      const { data, error } = await supabase
        .from("user_feedback")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error: any) {
      console.error("Get user feedback error:", error);
      throw new Error(error.message || "Failed to fetch user feedback");
    }
  },

  // Admin: Get all feedback with pagination and filters
  async getAllFeedback(
    page: number = 1,
    limit: number = 20,
    filters: FeedbackFilters = {}
  ): Promise<PaginatedFeedbackResponse> {
    try {
      let query = supabase.from("user_feedback").select(
        `
          *,
          profiles!user_id (
            full_name,
            email,
            role
          )
        `,
        { count: "exact" }
      );

      // Apply filters
      if (filters.status) {
        query = query.eq("status", filters.status);
      }
      if (filters.category) {
        query = query.eq("category", filters.category);
      }
      if (filters.rating) {
        query = query.eq("rating", filters.rating);
      }
      if (filters.dateFrom) {
        query = query.gte("created_at", filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.lte("created_at", filters.dateTo);
      }
      if (filters.search) {
        query = query.or(
          `feedback_text.ilike.%${filters.search}%,admin_notes.ilike.%${filters.search}%`
        );
      }

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      const { data, error, count } = await query
        .order("created_at", { ascending: false })
        .range(from, to);

      if (error) throw error;

      const totalPages = Math.ceil((count || 0) / limit);

      return {
        data: data || [],
        total: count || 0,
        page,
        limit,
        totalPages,
      };
    } catch (error: any) {
      console.error("Get all feedback error:", error);
      throw new Error(error.message || "Failed to fetch feedback");
    }
  },

  // Admin: Update feedback status and notes
  async updateFeedback(
    feedbackId: string,
    updates: {
      status?: FeedbackStatus;
      admin_notes?: string;
    }
  ): Promise<UserFeedback> {
    try {
      const { data, error } = await supabase
        .from("user_feedback")
        .update(updates)
        .eq("id", feedbackId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error: any) {
      console.error("Update feedback error:", error);
      throw new Error(error.message || "Failed to update feedback");
    }
  },

  // Admin: Delete feedback
  async deleteFeedback(feedbackId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("user_feedback")
        .delete()
        .eq("id", feedbackId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Delete feedback error:", error);
      throw new Error(error.message || "Failed to delete feedback");
    }
  },

  // Admin: Get feedback statistics (optimized with parallel queries and caching)
  async getFeedbackStats(): Promise<FeedbackStats> {
    // Check cache first
    if (
      feedbackStatsCache &&
      Date.now() - feedbackStatsCache.timestamp < FEEDBACK_CACHE_DURATION
    ) {
      return feedbackStatsCache.data;
    }

    try {
      // Execute all statistics queries in parallel for better performance
      const [
        totalResult,
        pendingResult,
        reviewedResult,
        resolvedResult,
        ratingResult,
        categoryResult,
        // recentResult,
      ] = await Promise.all([
        // Total feedback count
        supabase
          .from("user_feedback")
          .select("*", { count: "exact", head: true }),

        // Pending feedback count
        supabase
          .from("user_feedback")
          .select("*", { count: "exact", head: true })
          .eq("status", "pending"),

        // Reviewed feedback count
        supabase
          .from("user_feedback")
          .select("*", { count: "exact", head: true })
          .eq("status", "reviewed"),

        // Resolved feedback count
        supabase
          .from("user_feedback")
          .select("*", { count: "exact", head: true })
          .eq("status", "resolved"),

        // Get ratings for average calculation
        supabase.from("user_feedback").select("rating"),

        // Get categories for breakdown
        supabase.from("user_feedback").select("category"),

        // Get recent feedback count (last 7 days)
        supabase
          .from("user_feedback")
          .select("*", { count: "exact", head: true })
          .gte(
            "created_at",
            new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
          ),
      ]);

      // Calculate statistics from results
      const totalFeedback = totalResult.count || 0;
      const pendingFeedback = pendingResult.count || 0;
      const reviewedFeedback = reviewedResult.count || 0;
      const resolvedFeedback = resolvedResult.count || 0;
      // const recentFeedbackCount = recentResult.count || 0;

      // Calculate average rating
      const ratings = ratingResult.data || [];
      const averageRating =
        ratings.length > 0
          ? ratings.reduce((sum, f) => sum + f.rating, 0) / ratings.length
          : 0;

      // Calculate category breakdown
      const categories = categoryResult.data || [];
      const categoryBreakdown = {
        bug_report: categories.filter((f) => f.category === "bug_report")
          .length,
        feature_request: categories.filter(
          (f) => f.category === "feature_request"
        ).length,
        general: categories.filter((f) => f.category === "general").length,
        ui_ux: categories.filter((f) => f.category === "ui_ux").length,
      };

      // Calculate rating breakdown
      const ratingBreakdown = {
        1: ratings.filter((f) => f.rating === 1).length,
        2: ratings.filter((f) => f.rating === 2).length,
        3: ratings.filter((f) => f.rating === 3).length,
        4: ratings.filter((f) => f.rating === 4).length,
        5: ratings.filter((f) => f.rating === 5).length,
      };

      // For recent feedback, we'll get the actual records (limited query)
      const { data: recentFeedbackData } = await supabase
        .from("user_feedback")
        .select(
          `
          *,
          profiles!user_id (
            full_name,
            email,
            role
          )
        `
        )
        .order("created_at", { ascending: false })
        .limit(10);

      const recentFeedback = recentFeedbackData || [];

      const stats = {
        totalFeedback,
        pendingFeedback,
        reviewedFeedback,
        resolvedFeedback,
        averageRating,
        categoryBreakdown,
        ratingBreakdown,
        recentFeedback,
      };

      // Cache the results
      feedbackStatsCache = {
        data: stats,
        timestamp: Date.now(),
      };

      return stats;
    } catch (error: any) {
      console.error("Get feedback stats error:", error);
      throw new Error(error.message || "Failed to fetch feedback statistics");
    }
  },

  // Get feedback count by status for quick stats
  async getFeedbackCounts(): Promise<{
    total: number;
    pending: number;
    reviewed: number;
    resolved: number;
  }> {
    try {
      const { data, error } = await supabase
        .from("user_feedback")
        .select("status");

      if (error) throw error;

      const feedback = data || [];

      return {
        total: feedback.length,
        pending: feedback.filter((f) => f.status === "pending").length,
        reviewed: feedback.filter((f) => f.status === "reviewed").length,
        resolved: feedback.filter((f) => f.status === "resolved").length,
      };
    } catch (error: any) {
      console.error("Get feedback counts error:", error);
      throw new Error(error.message || "Failed to fetch feedback counts");
    }
  },
};
