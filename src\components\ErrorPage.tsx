import { <PERSON> } from "react-router-dom";
import { useTheme } from "../contexts/ThemeContext";
import { SEOHead } from "./seo/SEOHead";
import { generateSEOMetadata } from "../lib/seo";
import { BrimBagLogo } from "./ui/BrimBagLogo";
import { 
  HomeIcon, 
  DocumentTextIcon, 
  UserGroupIcon,
  ArrowLeftIcon 
} from "@heroicons/react/24/outline";

interface ErrorPageProps {
  errorCode?: number;
  title?: string;
  message?: string;
  showBackButton?: boolean;
}

export function ErrorPage({ 
  errorCode = 404, 
  title = "Page Not Found",
  message = "The page you're looking for doesn't exist or has been moved.",
  showBackButton = true
}: ErrorPageProps) {
  const { theme, toggleTheme } = useTheme();

  // Generate SEO metadata for error page
  const seoMetadata = generateSEOMetadata({
    title: `${errorCode} - ${title}`,
    description: `${title} - ${message} Return to BrimBag to access your documents and continue your work.`,
    noIndex: true, // Don't index error pages
    type: "website"
  });

  const getErrorIcon = () => {
    switch (errorCode) {
      case 403:
        return "🔒";
      case 500:
        return "⚠️";
      default:
        return "🔍";
    }
  };

  const getHelpfulLinks = () => {
    return [
      {
        href: "/",
        icon: HomeIcon,
        label: "Go Home",
        description: "Return to the main page"
      },
      {
        href: "/documents",
        icon: DocumentTextIcon,
        label: "My Documents",
        description: "Access your files"
      },
      {
        href: "/rooms",
        icon: UserGroupIcon,
        label: "Rooms",
        description: "Join study groups"
      }
    ];
  };

  return (
    <>
      {/* SEO Meta Tags */}
      <SEOHead metadata={seoMetadata} />
      
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900 transition-theme duration-theme">
        {/* Header */}
        <header className="relative z-10">
          <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-xl flex items-center justify-center shadow-lg">
                  <BrimBagLogo size="xl" variant="white" className="animate-float" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white transition-theme duration-theme">
                    BrimBag
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Digital Document Storage
                  </p>
                </div>
              </div>

              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200"
                aria-label="Toggle theme"
              >
                {theme === "dark" ? (
                  <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                  </svg>
                )}
              </button>
            </div>
          </nav>
        </header>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            {/* Error Icon */}
            <div className="mb-8">
              <div className="text-8xl mb-4">{getErrorIcon()}</div>
              <div className="text-6xl font-bold text-gray-900 dark:text-white mb-2 transition-theme duration-theme">
                {errorCode}
              </div>
            </div>

            {/* Error Message */}
            <div className="mb-12">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 transition-theme duration-theme">
                {title}
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto transition-theme duration-theme">
                {message}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              {showBackButton && (
                <button
                  onClick={() => window.history.back()}
                  className="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-800 hover:bg-gray-50 dark:hover:bg-dark-700 transition-all duration-200 shadow-sm"
                >
                  <ArrowLeftIcon className="w-5 h-5 mr-2" />
                  Go Back
                </button>
              )}
              
              <Link
                to="/"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <HomeIcon className="w-5 h-5 mr-2" />
                Return Home
              </Link>
            </div>

            {/* Helpful Links */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
              {getHelpfulLinks().map((link, index) => (
                <Link
                  key={index}
                  to={link.href}
                  className="group p-6 bg-white dark:bg-dark-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-200 dark:border-dark-700"
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors duration-200">
                      <link.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2 transition-theme duration-theme">
                      {link.label}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                      {link.description}
                    </p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="mt-16 py-8 border-t border-gray-200 dark:border-dark-700 transition-theme duration-theme">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p className="text-gray-600 dark:text-gray-400 transition-theme duration-theme">
              &copy; 2025 BrimBag. All rights reserved.
            </p>
          </div>
        </footer>
      </div>
    </>
  );
}
