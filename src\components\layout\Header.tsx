import { useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { NotificationsDropdown } from "../notifications/NotificationsDropdown";
import { ThemeToggle } from "../ui/ThemeToggle";
import { GlobalSearch } from "../search/GlobalSearch";

interface HeaderProps {
  onMenuClick: () => void;
}

export function Header({ onMenuClick }: HeaderProps) {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 dark:bg-dark-800 dark:border-dark-700 transition-theme duration-theme">
      <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        {/* Left side */}
        <div className="flex items-center">
          {/* Mobile menu button */}
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-400 dark:hover:bg-dark-700 transition-theme duration-theme"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>

          {/* Search bar */}
          <div className="hidden sm:block ml-4 lg:ml-0 w-80">
            <GlobalSearch />
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <NotificationsDropdown />

          {/* Theme toggle */}
          <ThemeToggle />

          {/* Quick upload */}
          <button
            onClick={() => navigate("/upload")}
            className="hidden sm:flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
            Upload
          </button>

          {/* Profile dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              className="flex items-center p-2 text-sm rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:hover:bg-dark-700 dark:focus:ring-blue-400 dark:focus:ring-offset-dark-800 transition-theme duration-theme"
            >
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center transition-theme duration-theme">
                <span className="text-blue-600 dark:text-blue-400 font-medium text-sm transition-theme duration-theme">
                  {user?.profile?.full_name?.charAt(0) ||
                    user?.email?.charAt(0) ||
                    "U"}
                </span>
              </div>
              <svg
                className="ml-2 w-4 h-4 text-gray-400 dark:text-gray-500 transition-theme duration-theme"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {/* Dropdown menu */}
            {isProfileMenuOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-gray-200 dark:border-dark-700 z-50 transition-theme duration-theme">
                <div className="py-1">
                  <div className="px-4 py-2 border-b border-gray-200 dark:border-dark-700 transition-theme duration-theme">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-theme duration-theme">
                      {user?.profile?.full_name || "User"}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 transition-theme duration-theme">
                      {user?.email}
                    </p>
                  </div>

                  <button
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-theme duration-theme"
                    onClick={() => {
                      navigate("/profile");
                      setIsProfileMenuOpen(false);
                    }}
                  >
                    Your Profile
                  </button>

                  <button
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-theme duration-theme"
                    onClick={() => {
                      navigate("/profile");
                      setIsProfileMenuOpen(false);
                    }}
                  >
                    Settings
                  </button>

                  <div className="border-t border-gray-200 dark:border-dark-700 transition-theme duration-theme">
                    <button
                      onClick={() => {
                        setIsProfileMenuOpen(false);
                        signOut(navigate);
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-theme duration-theme"
                    >
                      Sign out
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile search */}
      <div className="sm:hidden px-4 pb-4">
        <GlobalSearch />
      </div>
    </header>
  );
}
