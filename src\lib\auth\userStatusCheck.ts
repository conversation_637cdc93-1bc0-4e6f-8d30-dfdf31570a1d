import { supabase } from "../supabase";

/**
 * Check if the current user is active and handle deactivation
 */
export async function checkUserActiveStatus(): Promise<boolean> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return false;
    }

    // Check user's active status from profiles table
    const { data: profile, error } = await supabase
      .from("profiles")
      .select("active")
      .eq("id", user.id)
      .single();

    if (error) {
      console.error("Error checking user status:", error);
      return false;
    }

    // If user is inactive, sign them out
    if (!profile?.active) {
      await supabase.auth.signOut();
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in checkUserActiveStatus:", error);
    return false;
  }
}

/**
 * Set up real-time listener for user status changes
 */
export function setupUserStatusListener(
  userId: string,
  onDeactivated: () => void
) {
  const channel = supabase
    .channel("user-status-changes")
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "profiles",
        filter: `id=eq.${userId}`,
      },
      (payload) => {
        const newRecord = payload.new as any;

        // If user was deactivated, trigger logout
        if (newRecord.active === false) {
          onDeactivated();
        }
      }
    )
    .subscribe();

  return channel;
}

/**
 * Handle user deactivation - sign out and redirect
 */
export async function handleUserDeactivation(
  navigate?: (path: string) => void
) {
  try {
    await supabase.auth.signOut();

    // Use React Router navigation if available, otherwise fallback to window.location
    if (navigate) {
      navigate("/?message=account_deactivated");
    } else {
      window.location.href = "/?message=account_deactivated";
    }
  } catch (error) {
    console.error("Error during deactivation logout:", error);
    // Force redirect even if signOut fails
    if (navigate) {
      navigate("/?message=account_deactivated");
    } else {
      window.location.href = "/?message=account_deactivated";
    }
  }
}

/**
 * Middleware function to be used in protected routes
 */
export async function requireActiveUser(
  navigate?: (path: string) => void
): Promise<boolean> {
  const isActive = await checkUserActiveStatus();

  if (!isActive) {
    // User is either not logged in or deactivated
    if (navigate) {
      navigate("/?message=account_deactivated");
    } else {
      window.location.href = "/?message=account_deactivated";
    }
    return false;
  }

  return true;
}
