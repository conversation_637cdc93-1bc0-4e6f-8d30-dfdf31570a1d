import React from "react";
// import { Navigate, useLocation } from "react-router-dom";
import { Navigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { BrimBagLogo } from "./ui/BrimBagLogo";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  // const location = useLocation();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900 transition-theme duration-theme">
        <div className="text-center">
          <div className="mb-4">
            <BrimBagLogo size="xl" className="mx-auto animate-pulse" />
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
            Loading...
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    // Redirect to landing page instead of login to allow proper logout flow
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
}

interface PublicRouteProps {
  children: React.ReactNode;
}

export function PublicRoute({ children }: PublicRouteProps) {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900 transition-theme duration-theme">
        <div className="text-center">
          <div className="mb-4">
            <BrimBagLogo size="xl" className="mx-auto animate-pulse" />
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
            Loading...
          </p>
        </div>
      </div>
    );
  }

  if (user) {
    // Redirect to dashboard if already authenticated
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
}
