import { useEffect, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useTheme } from "../contexts/ThemeContext";
import { usePageTitle } from "../hooks/usePageTitle";
import { SEOHead } from "./seo/SEOHead";
import { getPageSEOData } from "../lib/seo";
import toast from "react-hot-toast";
import {
  FolderIcon,
  ShareIcon,
  UserGroupIcon,
  CloudArrowUpIcon,
  ShieldCheckIcon,
  DevicePhoneMobileIcon,
  SunIcon,
  MoonIcon,
  SparklesIcon,
} from "@heroicons/react/24/outline";
import { BrimBagLogo } from "./ui/BrimBagLogo";
import { CONTACT_EMAIL } from "../lib/constants";

export function LandingPage() {
  usePageTitle("Home");
  const { user, loading } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isVisible, setIsVisible] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  // Generate SEO metadata for landing page
  const seoMetadata = getPageSEOData("/");

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (!loading && user) {
      navigate("/dashboard", { replace: true });
    }
  }, [user, loading, navigate]);

  // Handle URL messages (like account deactivation)
  useEffect(() => {
    const message = searchParams.get("message");
    if (message === "account_deactivated") {
      toast.error(
        `Your account has been deactivated. Please contact support at ${CONTACT_EMAIL} if you believe this is an error.`
      );
      // Clear the message from URL
      navigate("/", { replace: true });
    }
  }, [searchParams, navigate]);

  // Animation trigger and scroll listener
  useEffect(() => {
    setIsVisible(true);

    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener("scroll", handleScroll);

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Don't render anything while checking auth or if user is authenticated
  if (loading || user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900 transition-theme duration-theme">
        <div className="text-center">
          <div className="mb-4">
            <BrimBagLogo size="xl" className="mx-auto animate-pulse" />
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400 transition-theme duration-theme">
            Loading...
          </p>
        </div>
      </div>
    );
  }

  const features = [
    {
      icon: CloudArrowUpIcon,
      title: "Smart Document Storage",
      description:
        "Upload and organize your documents with our intelligent folder system. Support for PDF, Office files, images, and more.",
    },
    {
      icon: UserGroupIcon,
      title: "Room-Based Sharing",
      description:
        "Create rooms for your classes or projects. Share documents with specific groups and collaborate seamlessly.",
    },
    {
      icon: ShareIcon,
      title: "Direct User Sharing",
      description:
        "Share documents directly with other users. Transfer ownership and maintain full control over your files.",
    },
    {
      icon: FolderIcon,
      title: "Hierarchical Organization",
      description:
        "Create nested folders to keep your documents perfectly organized. Drag and drop for easy management.",
    },
    {
      icon: ShieldCheckIcon,
      title: "Secure & Private",
      description:
        "Your documents are protected with enterprise-grade security. Only you and authorized users can access your files.",
    },
    {
      icon: DevicePhoneMobileIcon,
      title: "Mobile Responsive",
      description:
        "Access your documents anywhere, anytime. Fully optimized for mobile devices with touch-friendly interface.",
    },
  ];

  return (
    <>
      {/* SEO Meta Tags */}
      <SEOHead metadata={seoMetadata} includeStructuredData={true} />

      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900 transition-theme duration-theme">
        {/* Header */}
        <header className="relative z-10">
          <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-5 md:gap-0 py-6">
              <div className="flex items-center space-x-3">
                {/* <div className="h-10 w-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow"> */}
                <div className="h-10 w-10 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow">
                  <BrimBagLogo
                    size="xl"
                    variant="white"
                    className="animate-float"
                  />
                </div>
                <span className="text-2xl font-bold text-gray-900 dark:text-white transition-theme duration-theme">
                  BrimBag
                </span>
                {/* Beta Badge */}
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 dark:from-purple-900/30 dark:to-pink-900/30 dark:text-purple-300 border border-purple-200 dark:border-purple-700 transition-theme duration-theme animate-gradient">
                  <SparklesIcon className="w-3 h-3 mr-1" />
                  BETA
                </span>

                {/* Theme Toggle */}
                <button
                  onClick={toggleTheme}
                  className="p-2 rounded-lg bg-white/80 dark:bg-dark-800/80 backdrop-blur-sm border border-gray-200 dark:border-dark-600 hover:bg-white dark:hover:bg-dark-700 transition-all duration-200 shadow-sm"
                  aria-label="Toggle theme"
                >
                  {theme === "light" ? (
                    <MoonIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  ) : (
                    <SunIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  )}
                </button>
              </div>

              <div className="hidden md:flex flex-col md:flex-row items-center space-x-4 w-full md:w-fit gap-2 md:gap-0">
                <Link
                  to="/login"
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white font-medium transition-theme duration-theme py-2 px-6 w-full md:w-fit"
                >
                  Sign In
                </Link>
                <Link
                  to="/register"
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 w-full md:w-fit"
                >
                  Get Started
                </Link>
              </div>
            </div>
          </nav>
        </header>

        {/* Hero Section */}
        <section className="relative overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 sm:py-24 lg:py-32">
            <div
              className={`text-center transform transition-all duration-1000 ${
                isVisible
                  ? "translate-y-0 opacity-100"
                  : "translate-y-10 opacity-0"
              }`}
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 transition-theme duration-theme">
                Replace Your Physical Bag with{" "}
                <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  Digital Storage
                </span>
              </h1>
              <p className="text-xl sm:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed transition-theme duration-theme">
                Store, organize, and share your documents seamlessly. Perfect
                for students, professionals, and anyone who wants to go
                paperless.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                <Link
                  to="/register"
                  className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 hover:scale-105 text-lg group"
                >
                  <span className="flex items-center justify-center">
                    Start Free Today
                    <svg
                      className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                      />
                    </svg>
                  </span>
                </Link>
                <Link
                  to="/login"
                  className="w-full sm:w-auto bg-white/80 dark:bg-dark-800/80 backdrop-blur-sm border border-gray-200 dark:border-dark-600 text-gray-900 dark:text-white font-semibold py-4 px-8 rounded-xl hover:bg-white dark:hover:bg-dark-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-2 hover:scale-105 text-lg"
                >
                  Sign In
                </Link>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 transition-theme duration-theme">
                    200MB
                  </div>
                  <div className="text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Free Storage
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 transition-theme duration-theme">
                    10+
                  </div>
                  <div className="text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    File Types
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 transition-theme duration-theme">
                    ∞
                  </div>
                  <div className="text-gray-600 dark:text-gray-400 transition-theme duration-theme">
                    Sharing Options
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Background decoration with parallax */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div
              className="absolute -top-40 -right-32 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float"
              style={{ transform: `translateY(${scrollY * 0.1}px)` }}
            ></div>
            <div
              className="absolute -bottom-40 -left-32 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-float"
              style={{
                transform: `translateY(${-scrollY * 0.1}px)`,
                animationDelay: "2s",
              }}
            ></div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 sm:py-24 bg-white/50 dark:bg-dark-800/50 backdrop-blur-sm transition-theme duration-theme">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4 transition-theme duration-theme">
                Everything You Need for Document Management
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto transition-theme duration-theme">
                Powerful features designed to make your digital life easier and
                more organized.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className={`bg-white dark:bg-dark-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-dark-700 transform hover:-translate-y-2 ${
                    isVisible
                      ? "translate-y-0 opacity-100"
                      : "translate-y-10 opacity-0"
                  }`}
                  style={{ transitionDelay: `${index * 100}ms` }}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mb-6 shadow-lg">
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3 transition-theme duration-theme">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed transition-theme duration-theme">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action Section */}
        <section className="py-20 sm:py-24 bg-gradient-to-r from-blue-600 to-indigo-600 relative overflow-hidden">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-6">
              Ready to Go Digital?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of users who have already made the switch to
              digital document management. Start your journey today!
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                to="/register"
                className="w-full sm:w-auto bg-white text-blue-600 font-semibold py-4 px-8 rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 text-lg"
              >
                Create Free Account
              </Link>
              <Link
                to="/login"
                className="w-full sm:w-auto bg-transparent border-2 border-white text-white font-semibold py-4 px-8 rounded-xl hover:bg-white hover:text-blue-600 transition-all duration-200 text-lg"
              >
                Sign In Now
              </Link>
            </div>

            {/* Beta Notice */}
            <div className="mt-8 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
              <div className="flex items-center justify-center space-x-2 text-blue-100">
                <SparklesIcon className="w-5 h-5" />
                <span className="font-medium">
                  Currently in Beta - Free access with all features included!
                </span>
              </div>
            </div>
          </div>

          {/* Background decoration */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-24 -right-24 w-48 h-48 bg-white/10 rounded-full blur-2xl"></div>
            <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-white/10 rounded-full blur-2xl"></div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 dark:bg-dark-950 text-white py-12 transition-theme duration-theme">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {/* Brand */}
              <div className="col-span-1 md:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="h-8 w-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                    <BrimBagLogo size="sm" variant="white" />
                  </div>
                  <span className="text-xl font-bold">BrimBag</span>
                </div>
                <p className="text-gray-400 mb-4 max-w-md">
                  The modern solution for document storage and sharing. Replace
                  your physical bag with smart digital organization.
                </p>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <SparklesIcon className="w-4 h-4" />
                  <span>Currently in Beta - Free for everyone!</span>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h3 className="font-semibold mb-4">Quick Links</h3>
                <ul className="space-y-2 text-gray-400">
                  <li>
                    <Link
                      to="/register"
                      className="hover:text-white transition-colors"
                    >
                      Get Started
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/login"
                      className="hover:text-white transition-colors"
                    >
                      Sign In
                    </Link>
                  </li>
                </ul>
              </div>

              {/* Features */}
              <div>
                <h3 className="font-semibold mb-4">Features</h3>
                <ul className="space-y-2 text-gray-400 text-sm">
                  <li>Document Storage</li>
                  <li>Room Sharing</li>
                  <li>User Sharing</li>
                  <li>Folder Organization</li>
                  <li>Mobile Access</li>
                  <li>Secure Storage</li>
                </ul>
              </div>

              {/* Contact */}
              <div>
                <h3 className="font-semibold mb-4">Contact</h3>
                <ul className="space-y-2 text-gray-400 text-sm">
                  <li>
                    <a
                      href={`mailto:${CONTACT_EMAIL}`}
                      className="hover:text-white transition-colors"
                    >
                      {CONTACT_EMAIL}
                    </a>
                  </li>
                  <li>Support & Inquiries</li>
                  <li>Account Issues</li>
                </ul>
              </div>
            </div>

            <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400 text-sm">
              <p>
                &copy; 2025 BrimBag. All rights reserved. Currently in Beta.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
